<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\ZoomMeeting;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

echo "=== Testing Meetings View Fix ===\n\n";

// Get a user to test with
$user = User::first();
if (!$user) {
    echo "No users found in database\n";
    exit;
}

echo "Testing with user: {$user->name} (ID: {$user->id})\n";

// Simulate authentication
Auth::login($user);

try {
    // Test the meetings index data
    $upcomingMeetings = ZoomMeeting::with(['course', 'attendances' => function($query) use ($user) {
            $query->where('user_id', $user->id);
        }])
        ->whereHas('course.enrollments', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->upcoming()
        ->orderBy('start_time')
        ->get();

    $activeMeetings = ZoomMeeting::with(['course', 'attendances' => function($query) use ($user) {
            $query->where('user_id', $user->id);
        }])
        ->whereHas('course.enrollments', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->active()
        ->get();

    echo "\nMeetings data loaded successfully:\n";
    echo "- Upcoming meetings: " . $upcomingMeetings->count() . "\n";
    echo "- Active meetings: " . $activeMeetings->count() . "\n";

    // Test if we can access a specific meeting
    $meeting = ZoomMeeting::whereHas('course.enrollments', function($query) use ($user) {
        $query->where('user_id', $user->id);
    })->first();

    if ($meeting) {
        echo "\nTesting meeting details:\n";
        echo "- Meeting: {$meeting->topic}\n";
        echo "- Course: {$meeting->course->title}\n";
        echo "- Status: {$meeting->status}\n";
        echo "- Can join: " . ($meeting->can_join ? 'Yes' : 'No') . "\n";
        
        $attendance = $meeting->getUserAttendance($user);
        echo "- User attendance: " . ($attendance ? 'Has attendance record' : 'No attendance record') . "\n";
    } else {
        echo "\nNo meetings found for this user\n";
    }

    echo "\n✓ All data queries working correctly\n";
    echo "✓ Views should now render without $slot errors\n";

} catch (Exception $e) {
    echo "\n✗ Error testing meetings data: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
