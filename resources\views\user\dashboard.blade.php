@extends('student.layout')

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">Dashboard</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="javascript: void(0);">Lernovate</a></li>
                                <li class="breadcrumb-item active">Dashboard</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <div class="row">
                <div class="col-xl-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="flex-1 overflow-hidden">
                                    <p class="text-truncate font-size-14 mb-2">Enrolled Courses</p>
                                    <h4 class="mb-0">{{ $enrolledCourses }}</h4>
                                </div>
                                <div class="text-primary">
                                    <i class="uil-book-alt font-size-24"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-body border-top py-3">
                            <div class="text-truncate">
                                <span class="badge badge-soft-success font-size-11">+ 2.65%</span>
                                <span class="text-muted ms-2">Since last month</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="flex-1 overflow-hidden">
                                    <p class="text-truncate font-size-14 mb-2">Completed Courses</p>
                                    <h4 class="mb-0">{{ $completedCourses }}</h4>
                                </div>
                                <div class="text-primary">
                                    <i class="uil-check-circle font-size-24"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-body border-top py-3">
                            <div class="text-truncate">
                                <span class="badge badge-soft-success font-size-11">+ 1.15%</span>
                                <span class="text-muted ms-2">Since last month</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="flex-1 overflow-hidden">
                                    <p class="text-truncate font-size-14 mb-2">Certificates Earned</p>
                                    <h4 class="mb-0">{{ $certificates }}</h4>
                                </div>
                                <div class="text-primary">
                                    <i class="uil-award font-size-24"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-body border-top py-3">
                            <div class="text-truncate">
                                <span class="badge badge-soft-success font-size-11">+ 3.20%</span>
                                <span class="text-muted ms-2">Since last month</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="flex-1 overflow-hidden">
                                    <p class="text-truncate font-size-14 mb-2">Study Hours</p>
                                    <h4 class="mb-0">{{ number_format($studyHours, 1) }}</h4>
                                </div>
                                <div class="text-primary">
                                    <i class="uil-clock font-size-24"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-body border-top py-3">
                            <div class="text-truncate">
                                <span class="badge badge-soft-success font-size-11">+ 8.35%</span>
                                <span class="text-muted ms-2">Since last month</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end row -->

            <div class="row">
                <div class="col-xl-8">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-sm-flex flex-wrap">
                                <h4 class="card-title mb-4">Learning Progress</h4>
                                <div class="ms-auto">
                                    <ul class="nav nav-pills">
                                        <li class="nav-item">
                                            <a class="nav-link active" href="#">Week</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" href="#">Month</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" href="#">Year</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div id="stacked-column-chart" class="apex-charts" dir="ltr"></div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">Recent Activities</h4>
                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle mb-0">
                                    <tbody>
                                        <tr>
                                            <td style="width: 50px;">
                                                <div class="avatar-sm">
                                                    <span class="avatar-title rounded-circle bg-primary">
                                                        <i class="uil-book-alt font-size-16"></i>
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                <h5 class="font-size-14 mb-1">Completed Module 3</h5>
                                                <p class="text-muted mb-0">Diagnostic Imaging Basics</p>
                                            </td>
                                            <td>
                                                <div class="text-end">
                                                    <h5 class="font-size-14 mb-0">2 hours ago</h5>
                                                    <p class="text-muted mb-0">Progress</p>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="avatar-sm">
                                                    <span class="avatar-title rounded-circle bg-success">
                                                        <i class="uil-award font-size-16"></i>
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                <h5 class="font-size-14 mb-1">Certificate Earned</h5>
                                                <p class="text-muted mb-0">Medical Terminology</p>
                                            </td>
                                            <td>
                                                <div class="text-end">
                                                    <h5 class="font-size-14 mb-0">1 day ago</h5>
                                                    <p class="text-muted mb-0">Achievement</p>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="avatar-sm">
                                                    <span class="avatar-title rounded-circle bg-warning">
                                                        <i class="uil-file-check-alt font-size-16"></i>
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                <h5 class="font-size-14 mb-1">Quiz Completed</h5>
                                                <p class="text-muted mb-0">Score: 85%</p>
                                            </td>
                                            <td>
                                                <div class="text-end">
                                                    <h5 class="font-size-14 mb-0">3 days ago</h5>
                                                    <p class="text-muted mb-0">Assessment</p>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end row -->

            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">Current Courses</h4>
                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle mb-0">
                                    <thead>
                                        <tr>
                                            <th scope="col">Course</th>
                                            <th scope="col">Instructor</th>
                                            <th scope="col">Progress</th>
                                            <th scope="col">Status</th>
                                            <th scope="col">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($currentCourses as $enrollment)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-3">
                                                        @if($enrollment->course->thumbnail)
                                                            <img src="{{ asset('storage/' . $enrollment->course->thumbnail) }}" alt="" class="img-fluid rounded-circle">
                                                        @else
                                                            <div class="avatar-title rounded-circle bg-primary">
                                                                {{ substr($enrollment->course->title, 0, 1) }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <div>
                                                        <h5 class="font-size-14 mb-1">{{ $enrollment->course->title }}</h5>
                                                        <p class="text-muted mb-0">{{ Str::limit($enrollment->course->short_description, 50) }}</p>
                                                        @if($enrollment->course->has_live_session)
                                                            <small class="text-info"><i class="uil-video me-1"></i>Has Live Sessions</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $enrollment->course->instructor->name ?? 'N/A' }}</td>
                                            <td>
                                                <div class="progress" style="height: 6px;">
                                                    <div class="progress-bar {{ $enrollment->isCompleted() ? 'bg-success' : ($enrollment->progress > 50 ? 'bg-primary' : 'bg-warning') }}" 
                                                         role="progressbar" 
                                                         style="width: {{ $enrollment->progress_percentage }}%" 
                                                         aria-valuenow="{{ $enrollment->progress_percentage }}" 
                                                         aria-valuemin="0" 
                                                         aria-valuemax="100"></div>
                                                </div>
                                                <span class="text-muted font-size-12">{{ $enrollment->progress_percentage }}%</span>
                                            </td>
                                            <td>
                                                @if($enrollment->isCompleted())
                                                    <span class="badge badge-soft-success font-size-12">Completed</span>
                                                @else
                                                    <span class="badge badge-soft-primary font-size-12">In Progress</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column gap-1">
                                                    @if($enrollment->isCompleted())
                                                        <a href="{{ route('user.certificates') }}" class="btn btn-success btn-sm">Certificate</a>
                                                    @else
                                                        <a href="{{ route('course.show', $enrollment->course->slug) }}" class="btn btn-primary btn-sm">Continue</a>
                                                    @endif

                                                    @if($enrollment->course->has_live_session)
                                                        @php
                                                            $activeMeeting = $enrollment->course->zoomMeetings()->active()->first();
                                                            $upcomingMeeting = $enrollment->course->zoomMeetings()->upcoming()->first();
                                                        @endphp

                                                        @if($activeMeeting && $activeMeeting->can_join)
                                                            <a href="{{ route('user.meetings.join', $activeMeeting->id) }}" class="btn btn-success btn-sm">
                                                                <i class="uil-external-link-alt me-1"></i>Join Live Meeting
                                                            </a>
                                                        @elseif($upcomingMeeting)
                                                            @if($upcomingMeeting->can_join)
                                                                <a href="{{ route('user.meetings.join', $upcomingMeeting->id) }}" class="btn btn-success btn-sm">
                                                                    <i class="uil-external-link-alt me-1"></i>Join Meeting
                                                                </a>
                                                            @else
                                                                <a href="{{ route('user.meetings.show', $upcomingMeeting->id) }}" class="btn btn-outline-info btn-sm">
                                                                    <i class="uil-clock me-1"></i>Meeting Details
                                                                </a>
                                                            @endif
                                                        @else
                                                            <a href="{{ route('user.meetings.index') }}" class="btn btn-outline-secondary btn-sm">
                                                                <i class="uil-calendar-alt me-1"></i>View Meetings
                                                            </a>
                                                        @endif
                                                    @endif

                                                    @if($enrollment->course->meeting_required_for_completion && !$enrollment->isCompleted())
                                                        <small class="text-warning">
                                                            <i class="uil-exclamation-triangle me-1"></i>Meeting attendance required
                                                        </small>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <p class="text-muted mb-0">No courses enrolled yet.</p>
                                                <a href="{{ route('visitors.courses') }}" class="btn btn-primary btn-sm mt-2">Browse Courses</a>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end row -->
        </div>
        <!-- container-fluid -->
    </div>
    <!-- End Page-content -->
</div>
@endsection
