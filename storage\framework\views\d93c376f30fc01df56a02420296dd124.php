<div data-simplebar class="sidebar-menu-scroll">

    <!--- Sidemenu -->
    <div id="sidebar-menu">
        <!-- Left Menu Start -->
        <ul class="metismenu list-unstyled" id="side-menu">
            <li class="menu-title">Main</li>

            <li>
                <a href="<?php echo e(route('user.dashboard')); ?>">
                    <i class="uil-home-alt"></i>
                    <span>Dashboard</span>
                </a>
            </li>

            <li class="menu-title">Learning</li>

            <li>
                <a href="javascript: void(0);" class="has-arrow waves-effect">
                    <i class="uil-book-alt"></i>
                    <span>My Courses</span>
                </a>
                <ul class="sub-menu" aria-expanded="false">
                    <li><a href="<?php echo e(route('user.courses.enrolled')); ?>">Enrolled Courses</a></li>
                    <li><a href="<?php echo e(route('user.courses.completed')); ?>">Completed Courses</a></li>
                    <li><a href="<?php echo e(route('user.courses.progress')); ?>">Course Progress</a></li>
                </ul>
            </li>


            <li class="menu-title">Account</li>

            <li>
                <a href="<?php echo e(route('profile.edit')); ?>">
                    <i class="uil-user"></i>
                    <span>Profile Settings</span>
                </a>
            </li>

            <li>
                <a href="<?php echo e(route('visitors.about')); ?>">
                    <i class="uil-info-circle"></i>
                    <span>About Us</span>
                </a>
            </li>

            <li>
                <a href="<?php echo e(route('logout')); ?>" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                    <i class="uil-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </li>

        </ul>
    </div>

</div>

<form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
    <?php echo csrf_field(); ?>
</form>

            

        </ul>
    </div>
    <!-- Sidebar -->
</div>
<?php /**PATH D:\Laravel-Apps\lernovate\resources\views/student/includes/sidebar.blade.php ENDPATH**/ ?>