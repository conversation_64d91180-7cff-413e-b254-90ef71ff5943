<p class="text-muted mb-3">
    <?php echo e(__('Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.')); ?>

</p>

<button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
    <?php echo e(__('Delete Account')); ?>

</button>

<!-- Delete Account Modal -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteAccountModalLabel"><?php echo e(__('Delete Account')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="<?php echo e(route('profile.destroy')); ?>">
                <?php echo csrf_field(); ?>
                <?php echo method_field('delete'); ?>
                <div class="modal-body">
                    <h6><?php echo e(__('Are you sure you want to delete your account?')); ?></h6>
                    <p class="text-muted small mb-3">
                        <?php echo e(__('Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.')); ?>

                    </p>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label"><?php echo e(__('Password')); ?></label>
                        <input type="password" class="form-control" id="password" name="password" placeholder="<?php echo e(__('Password')); ?>" required>
                        <?php $__errorArgs = ['password', 'userDeletion'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger mt-1"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-danger"><?php echo e(__('Delete Account')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php /**PATH D:\Laravel-Apps\lernovate\resources\views/profile/partials/delete-user-form.blade.php ENDPATH**/ ?>