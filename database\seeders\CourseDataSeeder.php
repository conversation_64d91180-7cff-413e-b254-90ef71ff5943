<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CourseDataSeeder extends Seeder
{
    public function run()
    {
        // Create categories
        $categories = [
            ['name' => 'Clinical Chemistry', 'slug' => 'clinical-chemistry', 'description' => 'Biochemical analysis and clinical chemistry techniques', 'color' => '#3498db', 'icon' => 'fas fa-flask', 'is_active' => true],
            ['name' => 'Hematology', 'slug' => 'hematology', 'description' => 'Blood analysis and coagulation studies', 'color' => '#e74c3c', 'icon' => 'fas fa-tint', 'is_active' => true],
            ['name' => 'Microbiology', 'slug' => 'microbiology', 'description' => 'Pathogen identification and infection control', 'color' => '#2ecc71', 'icon' => 'fas fa-microscope', 'is_active' => true],
            ['name' => 'Quality Management', 'slug' => 'quality-management', 'description' => 'ISO standards and quality systems', 'color' => '#f39c12', 'icon' => 'fas fa-award', 'is_active' => true],
            ['name' => 'Laboratory Safety', 'slug' => 'laboratory-safety', 'description' => 'Safety protocols and emergency procedures', 'color' => '#e67e22', 'icon' => 'fas fa-hard-hat', 'is_active' => true],
        ];

        foreach ($categories as $category) {
            Category::updateOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }

        // Create instructor users
        $instructors = [
            ['name' => 'Dr. Mary Wanjiku', 'email' => '<EMAIL>', 'role' => 'admin'],
            ['name' => 'Dr. James Kiprotich', 'email' => '<EMAIL>', 'role' => 'admin'],
            ['name' => 'Dr. Grace Muthoni', 'email' => '<EMAIL>', 'role' => 'admin'],
        ];

        foreach ($instructors as $instructor) {
            User::updateOrCreate(
                ['email' => $instructor['email']],
                [
                    'name' => $instructor['name'],
                    'password' => Hash::make('password'),
                    'role' => $instructor['role'],
                    'email_verified_at' => now(),
                ]
            );
        }
    }
}