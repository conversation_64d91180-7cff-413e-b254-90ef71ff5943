<form method="post" action="<?php echo e(route('password.update')); ?>">
    <?php echo csrf_field(); ?>
    <?php echo method_field('put'); ?>

    <div class="mb-3">
        <label for="update_password_current_password" class="form-label"><?php echo e(__('Current Password')); ?></label>
        <input type="password" class="form-control" id="update_password_current_password" name="current_password" autocomplete="current-password">
        <?php $__errorArgs = ['current_password', 'updatePassword'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-danger mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="mb-3">
        <label for="update_password_password" class="form-label"><?php echo e(__('New Password')); ?></label>
        <input type="password" class="form-control" id="update_password_password" name="password" autocomplete="new-password">
        <?php $__errorArgs = ['password', 'updatePassword'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-danger mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="mb-3">
        <label for="update_password_password_confirmation" class="form-label"><?php echo e(__('Confirm Password')); ?></label>
        <input type="password" class="form-control" id="update_password_password_confirmation" name="password_confirmation" autocomplete="new-password">
        <?php $__errorArgs = ['password_confirmation', 'updatePassword'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-danger mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="d-flex align-items-center gap-2">
        <button type="submit" class="btn btn-primary"><?php echo e(__('Save')); ?></button>

        <?php if(session('status') === 'password-updated'): ?>
            <span class="text-success small"><?php echo e(__('Saved.')); ?></span>
        <?php endif; ?>
    </div>
</form>
<?php /**PATH D:\Laravel-Apps\lernovate\resources\views/profile/partials/update-password-form.blade.php ENDPATH**/ ?>