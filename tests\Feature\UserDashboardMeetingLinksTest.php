<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Category;
use App\Models\Enrollment;
use App\Models\ZoomMeeting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserDashboardMeetingLinksTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $instructor;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->user = User::factory()->create([
            'role' => 'user',
            'email_verified_at' => now(),
        ]);
        
        $this->instructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);
        
        // Create test category
        $this->category = Category::factory()->create([
            'name' => 'Test Category',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function user_dashboard_shows_meeting_links_for_courses_with_live_sessions()
    {
        // Create a course with live sessions
        $course = Course::factory()->create([
            'title' => 'Course with Live Sessions',
            'status' => 'published',
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'has_live_session' => true,
            'meeting_required_for_completion' => true,
        ]);

        // Enroll user in the course
        $enrollment = Enrollment::create([
            'user_id' => $this->user->id,
            'course_id' => $course->id,
            'enrolled_at' => now(),
            'status' => 'enrolled',
            'progress' => 50,
        ]);

        // Create an upcoming meeting
        $meeting = ZoomMeeting::create([
            'course_id' => $course->id,
            'zoom_meeting_id' => '123456789',
            'topic' => 'Test Meeting',
            'start_time' => now()->addHours(2),
            'duration' => 60,
            'join_url' => 'https://zoom.us/j/123456789',
            'status' => 'scheduled',
            'created_by' => $this->instructor->id,
        ]);

        // Act as the user and visit dashboard
        $response = $this->actingAs($this->user)
            ->get(route('user.dashboard'));

        // Assert the response is successful
        $response->assertStatus(200);

        // Assert that live session indicator is shown
        $response->assertSee('Has Live Sessions');

        // Assert that meeting details link is shown
        $response->assertSee('Meeting Details');

        // Assert that meeting attendance requirement is shown
        $response->assertSee('Meeting attendance required');
    }

    /** @test */
    public function user_dashboard_shows_join_meeting_link_when_meeting_can_be_joined()
    {
        // Create a course with live sessions
        $course = Course::factory()->create([
            'title' => 'Course with Active Meeting',
            'status' => 'published',
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'has_live_session' => true,
        ]);

        // Enroll user in the course
        $enrollment = Enrollment::create([
            'user_id' => $this->user->id,
            'course_id' => $course->id,
            'enrolled_at' => now(),
            'status' => 'enrolled',
            'progress' => 50,
        ]);

        // Create a meeting that can be joined (within 15 minutes of start time)
        $meeting = ZoomMeeting::create([
            'course_id' => $course->id,
            'zoom_meeting_id' => '123456789',
            'topic' => 'Active Test Meeting',
            'start_time' => now()->addMinutes(10), // 10 minutes from now
            'duration' => 60,
            'join_url' => 'https://zoom.us/j/123456789',
            'status' => 'scheduled',
            'created_by' => $this->instructor->id,
        ]);

        // Act as the user and visit dashboard
        $response = $this->actingAs($this->user)
            ->get(route('user.dashboard'));

        // Assert the response is successful
        $response->assertStatus(200);

        // Assert that join meeting link is shown
        $response->assertSee('Join Meeting');
    }

    /** @test */
    public function enrolled_courses_page_shows_meeting_links()
    {
        // Create a course with live sessions
        $course = Course::factory()->create([
            'title' => 'Enrolled Course with Meetings',
            'status' => 'published',
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'has_live_session' => true,
        ]);

        // Enroll user in the course
        $enrollment = Enrollment::create([
            'user_id' => $this->user->id,
            'course_id' => $course->id,
            'enrolled_at' => now(),
            'status' => 'enrolled',
            'progress' => 30,
        ]);

        // Create an upcoming meeting
        $meeting = ZoomMeeting::create([
            'course_id' => $course->id,
            'zoom_meeting_id' => '987654321',
            'topic' => 'Enrolled Course Meeting',
            'start_time' => now()->addDays(1),
            'duration' => 90,
            'join_url' => 'https://zoom.us/j/987654321',
            'status' => 'scheduled',
            'created_by' => $this->instructor->id,
        ]);

        // Act as the user and visit enrolled courses page
        $response = $this->actingAs($this->user)
            ->get(route('user.courses.enrolled'));

        // Assert the response is successful
        $response->assertStatus(200);

        // Assert that live session indicator is shown
        $response->assertSee('Has Live Sessions');

        // Assert that view meetings link is shown
        $response->assertSee('View Meetings');
    }

    /** @test */
    public function completed_courses_page_shows_recording_links()
    {
        // Create a course with live sessions
        $course = Course::factory()->create([
            'title' => 'Completed Course with Recording',
            'status' => 'published',
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'has_live_session' => true,
        ]);

        // Enroll user in the course and mark as completed
        $enrollment = Enrollment::create([
            'user_id' => $this->user->id,
            'course_id' => $course->id,
            'enrolled_at' => now()->subDays(10),
            'completed_at' => now()->subDays(1),
            'status' => 'completed',
            'progress' => 100,
        ]);

        // Create a completed meeting with recording
        $meeting = ZoomMeeting::create([
            'course_id' => $course->id,
            'zoom_meeting_id' => '555666777',
            'topic' => 'Completed Meeting with Recording',
            'start_time' => now()->subDays(2),
            'duration' => 60,
            'join_url' => 'https://zoom.us/j/555666777',
            'status' => 'ended',
            'recording_available' => true,
            'recording_url' => 'https://zoom.us/rec/555666777',
            'created_by' => $this->instructor->id,
        ]);

        // Act as the user and visit completed courses page
        $response = $this->actingAs($this->user)
            ->get(route('user.courses.completed'));

        // Assert the response is successful
        $response->assertStatus(200);

        // Assert that live session indicator is shown
        $response->assertSee('Had Live Sessions');

        // Assert that view recording link is shown
        $response->assertSee('View Recording');
    }

    /** @test */
    public function courses_without_live_sessions_do_not_show_meeting_links()
    {
        // Create a course without live sessions
        $course = Course::factory()->create([
            'title' => 'Course without Live Sessions',
            'status' => 'published',
            'category_id' => $this->category->id,
            'instructor_id' => $this->instructor->id,
            'has_live_session' => false,
        ]);

        // Enroll user in the course
        $enrollment = Enrollment::create([
            'user_id' => $this->user->id,
            'course_id' => $course->id,
            'enrolled_at' => now(),
            'status' => 'enrolled',
            'progress' => 75,
        ]);

        // Act as the user and visit dashboard
        $response = $this->actingAs($this->user)
            ->get(route('user.dashboard'));

        // Assert the response is successful
        $response->assertStatus(200);

        // Assert that live session indicators are NOT shown
        $response->assertDontSee('Has Live Sessions');
        $response->assertDontSee('Join Meeting');
        $response->assertDontSee('Meeting Details');
        $response->assertDontSee('View Meetings');
    }
}
