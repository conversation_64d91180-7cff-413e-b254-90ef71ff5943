<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['course', 'showWishlist' => true]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['course', 'showWishlist' => true]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="courses__item shine__animate-item">
    <div class="courses__item-thumb">
        <a href="<?php echo e(route('course.show', $course->slug)); ?>" class="shine__animate-link">
            <?php if($course->thumbnail): ?>
                <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>" alt="<?php echo e($course->title); ?>">
            <?php else: ?>
                <img src="<?php echo e(asset('frontend/img/course/course_thumb01.jpg')); ?>" alt="<?php echo e($course->title); ?>">
            <?php endif; ?>
        </a>
        <?php if($showWishlist): ?>
            <a href="javascript:;" class="wsus-wishlist-btn common-white courses__wishlist-two"
               aria-label="WishList" data-slug="<?php echo e($course->slug); ?>">
                <i class="far fa-heart"></i>
            </a>
        <?php endif; ?>
    </div>
    <div class="courses__item-content">
        <ul class="courses__item-meta list-wrap">
            <li class="courses__item-tag">
                <a href="<?php echo e(route('visitors.courses')); ?>?category=<?php echo e($course->category->slug); ?>">
                    <?php echo e($course->category->name); ?>

                </a>
            </li>
            <li class="avg-rating">
                <i class="fas fa-star"></i>
                5.0 
            </li>
        </ul>
        <h3 class="title">
            <a href="<?php echo e(route('course.show', $course->slug)); ?>"><?php echo e($course->title); ?></a>
        </h3>
        <p class="author">
            By <a href="#" class="instructor-link"><?php echo e($course->instructor->name); ?></a>
        </p>
        <div class="courses__item-bottom">
            <div class="button">
                <?php if($course->is_free): ?>
                    <a href="<?php echo e(route('course.show', $course->slug)); ?>" class="enroll-free">
                        <span class="text">Enroll Free</span>
                        <i class="flaticon-arrow-right"></i>
                    </a>
                <?php else: ?>
                    <a href="javascript:;" class="add-to-cart" data-id="<?php echo e($course->id); ?>" data-title="<?php echo e($course->title); ?>">
                        <span class="text">Add To Cart</span>
                        <i class="flaticon-arrow-right"></i>
                    </a>
                <?php endif; ?>
            </div>

            <h4 class="price">
                <?php if($course->is_free): ?>
                    Free
                <?php elseif($course->discount_price && $course->discount_price < $course->price): ?>
                    <span class="old-price">KES <?php echo e(number_format($course->price, 2)); ?></span>
                    KES <?php echo e(number_format($course->discount_price, 2)); ?>

                <?php else: ?>
                    KES <?php echo e(number_format($course->price ?? 0, 2)); ?>

                <?php endif; ?>
            </h4>
        </div>
    </div>
</div>
<?php /**PATH D:\Laravel-Apps\lernovate\resources\views/components/course-card.blade.php ENDPATH**/ ?>