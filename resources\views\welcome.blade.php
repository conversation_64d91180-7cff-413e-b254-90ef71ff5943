<!doctype html>
<html class="no-js" lang="en">


<meta http-equiv="content-type" content="text/html;charset=UTF-8" />

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Home || {{ \App\Models\SiteSetting::get('site_name', 'Lernovate') }}</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="Home || Skillgro">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Custom Meta -->
    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="uploads/website-images/favicon.png">
    <!-- CSS here -->
    <link rel="stylesheet" href="frontend/css/bootstrap.min.css">
    <link rel="stylesheet" href="frontend/css/animate.min.css">
    <link rel="stylesheet" href="frontend/css/magnific-popup.css">
    <link rel="stylesheet" href="frontend/css/fontawesome-all.min.css">
    <link rel="stylesheet" href="frontend/css/flaticon-skillgro.css">
    <link rel="stylesheet" href="frontend/css/swiper-bundle.min.css">
    <link rel="stylesheet" href="frontend/css/default-icons.css">
    <link rel="stylesheet" href="frontend/css/select2.min.css">
    <link rel="stylesheet" href="frontend/css/odometer.css">
    <link rel="stylesheet" href="frontend/css/aos.css">
    <link rel="stylesheet" href="frontend/css/plyr.css">
    <link rel="stylesheet" href="frontend/css/spacing.css">
    <link rel="stylesheet" href="frontend/css/tg-cursor.css">
    <link rel="stylesheet" href="frontend/css/bootstrap-datepicker.min.css">
    <link rel="stylesheet" href="global/toastr/toastr.min.css">
    <link rel="stylesheet" href="global/nice-select/nice-select.css">
    <link rel="stylesheet" href="frontend/css/main.minc669.css?v=2.5.0">
    <link rel="stylesheet" href="frontend/css/frontend.minc669.css?v=2.5.0">



    <style>
        :root {
            --tg-theme-primary: #5751e1;
            --tg-theme-secondary: #ffc224;
            --tg-common-color-blue: #050071;
            --tg-common-color-blue-2: #282568;
            --tg-common-color-dark: #1c1a4a;
            --tg-common-color-black: #06042e;
            --tg-common-color-dark-2: #4a44d1;
        }
    </style>


    <script>
        "use strict";
        //write your javascript here without the script tag
    </script>

</head>

<body class="home_main">


    <!-- Scroll-top -->
    <button class="scroll__top scroll-to-target" data-target="html" aria-label="Scroll Top">
        <i class="tg-flaticon-arrowhead-up"></i>
    </button>
    <!-- Scroll-top-end-->

    <!-- header-area -->
    <!-- header-area -->
    @include('includes.header')
    <!-- header-area-end -->
    <!-- header-area-end -->

    <!-- main-area -->
   @yield('content')
    <!-- main-area-end -->

    <!-- modal-area -->
    <!-- Modal -->
    <div class="modal fade dynamic-modal modal-lg" tabindex="-1" aria-labelledby="dynamic-modalLabel" aria-hidden="true"
        data-bs-backdrop='static'>
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="d-flex justify-content-center align-items:center p-3">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="modal fade bd-example-modal-lg" id="iframeModal" data-bs-backdrop="static" tabindex="-1"
        aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <h5>Your are using this website under an external iframe</h5>
                    <p>For a better experience please browse directly instead of an external iframe</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <a target="_blank" href="index.html" class="btn btn-sm btn-primary">Browse Directly</a>
                </div>
            </div>
        </div>
    </div> <!-- Modal -->

    <!-- Cart Modal -->
    <div class="modal fade" id="cartModal" tabindex="-1" aria-labelledby="cartModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="cartModalLabel">
                        <i class="fas fa-shopping-cart me-2"></i>Shopping Cart
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="cart-modal-body">
                    <div class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"
        data-bs-backdrop='static'>
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">Chapter Title</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="https://skillgro.websolutionus.com/instructor/course-chapter//store"
                        class="instructor__profile-form" method="post">
                        @csrf
                        <div class="col-md-12">
                            <div class="form-grp">
                                <label for="title">Title <code>*</code></label>
                                <input id="title" name="title" type="text" value="">
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Create</button>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- modal-area -->

    <!-- footer-area -->
   @include('includes.footer')
    <!-- footer-area-end -->


    <!-- JS here -->
    <script src="global/js/jquery-3.7.1.min.js"></script>
    <script src="frontend/js/proper.min.js"></script>
    <script src="frontend/js/bootstrap.min.js"></script>
    <script src="frontend/js/imagesloaded.pkgd.min.js"></script>
    <script src="frontend/js/jquery.magnific-popup.min.js"></script>
    <script src="frontend/js/jquery.odometer.min.js"></script>
    <script src="frontend/js/jquery.appear.js"></script>
    <script src="frontend/js/tween-max.min.js"></script>
    <script src="frontend/js/select2.min.js"></script>
    <script src="frontend/js/swiper-bundle.min.js"></script>
    <script src="frontend/js/jquery.marquee.min.js"></script>
    <script src="frontend/js/tg-cursor.min.js"></script>
    <script src="frontend/js/svg-inject.min.js"></script>
    <script src="frontend/js/jquery.circleType.js"></script>
    <script src="frontend/js/jquery.lettering.min.js"></script>
    <script src="frontend/js/bootstrap-datepicker.min.js"></script>
    <script src="frontend/js/plyr.min.js"></script>
    <script src="frontend/js/wow.min.js"></script>
    <script src="frontend/js/aos.js"></script>
    <script src="frontend/js/vivus.min.js"></script>
    <script src="global/toastr/toastr.min.js"></script>
    <script src="frontend/js/sweetalert.js"></script>
    <script src="frontend/js/default/frontendc669.js?v=2.5.0"></script>
    <script src="frontend/js/default/cartc669.js?v=2.5.0"></script>
    <script src="global/nice-select/jquery.nice-select.min.js"></script>
    <!-- File Manager js-->
    <script src="vendor/laravel-filemanager/js/stand-alone-button.js"></script>


    <script src="frontend/js/mainc669.js?v=2.5.0"></script>

    <script>
        $('.file-manager').filemanager('file', {
        prefix: 'https://skillgro.websolutionus.com/frontend-filemanager'
    });
    $('.file-manager-image').filemanager('image', {
        prefix: 'https://skillgro.websolutionus.com/frontend-filemanager'
    });

    SVGInject(document.querySelectorAll("img.injectable"));
    </script>

    <!-- dynamic Toastr Notification -->
    <script>
        "use strict";
    toastr.options.closeButton = true;
    toastr.options.progressBar = true;
    toastr.options.positionClass = 'toast-bottom-right';


    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        orientation: "bottom auto"
    });
    </script>


    <!-- Toastr -->


    <!-- Google reCAPTCHA -->

    <!-- tawk -->

    <!-- Cookie Consent Removed -->

    <script>
        if ($(".marquee_mode").length) {
        $('.marquee_mode').marquee({
            speed: 20,
            gap: 35,
            delayBeforeStart: 0,
            direction: "left",
            duplicated: true,
            pauseOnHover: true,
            startVisible: true,
        });
    }
    </script>

    <script>
        $(document).on("click", '.wpcc-btn', function() {
        $('.wpcc-container').fadeOut(1000);
    });
    </script>

    <!-- Language Translation Variables -->
    <script>
        var base_url = "{{ url('/') }}";
  var preloader_path = "uploads/custom-images/wsus-img-2024-06-06-05-37-49-1116.svg";

  var demo_mode_error = "This Is Demo Version. You Can Not Change Anything";
  var translation_success = "Translated Successfully!";
  var translation_processing = "Translation Processing, please wait...";
  var search_instructor_placeholder = "Search for an instructor with email or name";
  var Previous = "Previous";
  var Next = "Next";
  var basic_error_message = "Something went wrong";
  var discount = "Discount";
  var subscribe_now = "Subscribe Now";
  var submitting = "Submitting...";
  var submitting = "Submitting...";
  var login_first = "Login first";
    </script>

    <script>
        "use strict";

    </script>
</body>


</html>
