@extends('admin.dashboard')
@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">

            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Reports Overview</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Reports</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <!-- Key Metrics Cards -->
            <div class="row">
                <div class="col-md-6 col-xl-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="float-end mt-2">
                                <div class="avatar-sm">
                                    <span class="avatar-title bg-primary rounded-circle">
                                        <i class="uil-graduation-cap font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-1 mt-1"><span data-plugin="counterup">{{ $totalCourses }}</span></h4>
                                <p class="text-muted mb-0">Published Courses</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xl-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="float-end mt-2">
                                <div class="avatar-sm">
                                    <span class="avatar-title bg-success rounded-circle">
                                        <i class="uil-users-alt font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-1 mt-1"><span data-plugin="counterup">{{ $totalEnrollments }}</span></h4>
                                <p class="text-muted mb-0">Total Enrollments</p>
                            </div>
                            <p class="text-muted mt-3 mb-0">
                                <span class="text-success me-1">
                                    <i class="mdi mdi-arrow-up-bold me-1"></i>{{ $recentEnrollments }}
                                </span> in last 30 days
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xl-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="float-end mt-2">
                                <div class="avatar-sm">
                                    <span class="avatar-title bg-warning rounded-circle">
                                        <i class="uil-money-bill font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-1 mt-1">KES <span data-plugin="counterup">{{ number_format($totalRevenue, 0) }}</span></h4>
                                <p class="text-muted mb-0">Total Revenue</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xl-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="float-end mt-2">
                                <div class="avatar-sm">
                                    <span class="avatar-title bg-info rounded-circle">
                                        <i class="uil-check-circle font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-1 mt-1"><span data-plugin="counterup">{{ $complianceRate }}</span>%</h4>
                                <p class="text-muted mb-0">Attendance Compliance</p>
                            </div>
                            <p class="text-muted mt-3 mb-0">
                                <small>KMLTTB 45-min requirement</small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Access to Reports -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="avatar-md me-4">
                                    <span class="avatar-title rounded-circle bg-light text-primary">
                                        <i class="uil-chart-line font-size-20"></i>
                                    </span>
                                </div>
                                <div class="flex-grow-1 overflow-hidden">
                                    <h5 class="text-truncate font-size-15">Course Registration Reports</h5>
                                    <p class="text-muted mb-4">View detailed enrollment data, revenue analysis, and user information including ID No and KMLTTB No for regulatory compliance.</p>
                                    <a href="{{ route('admin.reports.registrations') }}" class="btn btn-primary btn-sm">
                                        <i class="uil-external-link-alt me-1"></i> View Registration Reports
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="avatar-md me-4">
                                    <span class="avatar-title rounded-circle bg-light text-success">
                                        <i class="uil-clock font-size-20"></i>
                                    </span>
                                </div>
                                <div class="flex-grow-1 overflow-hidden">
                                    <h5 class="text-truncate font-size-15">Meeting Attendance Reports</h5>
                                    <p class="text-muted mb-4">Track attendance compliance with KMLTTB 45-minute requirement, monitor CPD credit eligibility, and identify non-compliant participants.</p>
                                    <a href="{{ route('admin.reports.attendance') }}" class="btn btn-success btn-sm">
                                        <i class="uil-external-link-alt me-1"></i> View Attendance Reports
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Performing Courses -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h4 class="card-title">Top Performing Courses</h4>
                                <a href="{{ route('admin.reports.registrations') }}" class="btn btn-outline-primary btn-sm">
                                    View All <i class="uil-arrow-right ms-1"></i>
                                </a>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Course Title</th>
                                            <th>Enrollments</th>
                                            <th>Price (KES)</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($topCourses as $course)
                                        <tr>
                                            <td>
                                                <h6 class="mb-0">{{ $course->title }}</h6>
                                                <p class="text-muted font-size-13 mb-0">{{ Str::limit($course->short_description, 50) }}</p>
                                            </td>
                                            <td>
                                                <span class="badge badge-soft-primary font-size-12">{{ $course->enrollments_count }} enrolled</span>
                                            </td>
                                            <td>
                                                <span class="text-success font-weight-bold">
                                                    KES {{ number_format($course->price, 0) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($course->status === 'published')
                                                    <span class="badge badge-soft-success">Published</span>
                                                @else
                                                    <span class="badge badge-soft-warning">{{ ucfirst($course->status) }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="4" class="text-center text-muted py-4">
                                                <i class="uil-graduation-cap font-size-24 mb-2 d-block"></i>
                                                No courses available
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title mb-4">KMLTTB Compliance Summary</h4>
                            
                            <div class="text-center">
                                <div class="mb-4">
                                    <i class="uil-shield-check text-success font-size-48"></i>
                                </div>
                                <h3 class="text-success">{{ $complianceRate }}%</h3>
                                <p class="text-muted">Overall Compliance Rate</p>
                            </div>

                            <div class="mt-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-muted">KMLTTB Registered Users</span>
                                    <span class="font-weight-bold">{{ $kmlttbCompliance['total_kmlttb_users'] ?? 0 }}</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-muted">Total CPD Hours Earned</span>
                                    <span class="font-weight-bold text-success">{{ $kmlttbCompliance['total_cpd_hours'] ?? 0 }} hours</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-muted">Avg CPD per User</span>
                                    <span class="font-weight-bold">{{ $kmlttbCompliance['average_cpd_per_user'] ?? 0 }} hours</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">Regulatory Standard</span>
                                    <span class="font-weight-bold text-primary">45 min minimum</span>
                                </div>
                            </div>

                            <div class="mt-4">
                                <a href="{{ route('admin.reports.attendance') }}" class="btn btn-outline-success btn-block">
                                    <i class="uil-analytics me-1"></i> View Detailed Compliance
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Regulatory Compliance Indicators -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div>
                                    <h4 class="card-title text-info">
                                        <i class="uil-shield-check me-2"></i>KMLTTB Regulatory Compliance
                                    </h4>
                                    <p class="card-title-desc">Kenya Medical Laboratory Technicians and Technologists Board compliance indicators</p>
                                </div>
                            </div>

                            <div class="row">
                                @if(isset($regulatoryIndicators['kmlttb_registration']))
                                <div class="col-md-4">
                                    <div class="card border-{{ $regulatoryIndicators['kmlttb_registration']['status'] === 'excellent' ? 'success' : ($regulatoryIndicators['kmlttb_registration']['status'] === 'good' ? 'warning' : 'danger') }}">
                                        <div class="card-body text-center">
                                            <div class="avatar-sm mx-auto mb-3">
                                                <span class="avatar-title rounded-circle bg-{{ $regulatoryIndicators['kmlttb_registration']['status'] === 'excellent' ? 'success' : ($regulatoryIndicators['kmlttb_registration']['status'] === 'good' ? 'warning' : 'danger') }} text-white">
                                                    <i class="uil-user-check font-size-16"></i>
                                                </span>
                                            </div>
                                            <h5 class="text-{{ $regulatoryIndicators['kmlttb_registration']['status'] === 'excellent' ? 'success' : ($regulatoryIndicators['kmlttb_registration']['status'] === 'good' ? 'warning' : 'danger') }}">
                                                {{ $regulatoryIndicators['kmlttb_registration']['rate'] }}%
                                            </h5>
                                            <p class="text-muted mb-2">KMLTTB Registration Rate</p>
                                            <small class="text-muted">{{ $regulatoryIndicators['kmlttb_registration']['message'] }}</small>
                                        </div>
                                    </div>
                                </div>
                                @endif

                                @if(isset($regulatoryIndicators['attendance_compliance']))
                                <div class="col-md-4">
                                    <div class="card border-{{ $regulatoryIndicators['attendance_compliance']['status'] === 'excellent' ? 'success' : ($regulatoryIndicators['attendance_compliance']['status'] === 'good' ? 'warning' : 'danger') }}">
                                        <div class="card-body text-center">
                                            <div class="avatar-sm mx-auto mb-3">
                                                <span class="avatar-title rounded-circle bg-{{ $regulatoryIndicators['attendance_compliance']['status'] === 'excellent' ? 'success' : ($regulatoryIndicators['attendance_compliance']['status'] === 'good' ? 'warning' : 'danger') }} text-white">
                                                    <i class="uil-clock font-size-16"></i>
                                                </span>
                                            </div>
                                            <h5 class="text-{{ $regulatoryIndicators['attendance_compliance']['status'] === 'excellent' ? 'success' : ($regulatoryIndicators['attendance_compliance']['status'] === 'good' ? 'warning' : 'danger') }}">
                                                {{ $regulatoryIndicators['attendance_compliance']['rate'] }}%
                                            </h5>
                                            <p class="text-muted mb-2">Attendance Compliance</p>
                                            <small class="text-muted">{{ $regulatoryIndicators['attendance_compliance']['message'] }}</small>
                                        </div>
                                    </div>
                                </div>
                                @endif

                                @if(isset($regulatoryIndicators['cpd_hours']))
                                <div class="col-md-4">
                                    <div class="card border-{{ $regulatoryIndicators['cpd_hours']['status'] === 'excellent' ? 'success' : ($regulatoryIndicators['cpd_hours']['status'] === 'good' ? 'warning' : 'danger') }}">
                                        <div class="card-body text-center">
                                            <div class="avatar-sm mx-auto mb-3">
                                                <span class="avatar-title rounded-circle bg-{{ $regulatoryIndicators['cpd_hours']['status'] === 'excellent' ? 'success' : ($regulatoryIndicators['cpd_hours']['status'] === 'good' ? 'warning' : 'danger') }} text-white">
                                                    <i class="uil-award font-size-16"></i>
                                                </span>
                                            </div>
                                            <h5 class="text-{{ $regulatoryIndicators['cpd_hours']['status'] === 'excellent' ? 'success' : ($regulatoryIndicators['cpd_hours']['status'] === 'good' ? 'warning' : 'danger') }}">
                                                {{ $regulatoryIndicators['cpd_hours']['average'] }}
                                            </h5>
                                            <p class="text-muted mb-2">Avg CPD Hours per User</p>
                                            <small class="text-muted">{{ $regulatoryIndicators['cpd_hours']['message'] }}</small>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>

                            <div class="mt-4 p-3 bg-light rounded">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <h6 class="text-muted mb-1">Regulatory Body</h6>
                                        <p class="mb-0 font-weight-bold">KMLTTB</p>
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="text-muted mb-1">Minimum Attendance</h6>
                                        <p class="mb-0 font-weight-bold">45 Minutes</p>
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="text-muted mb-1">CPD Requirement</h6>
                                        <p class="mb-0 font-weight-bold">Annual Credits</p>
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="text-muted mb-1">Compliance Status</h6>
                                        <p class="mb-0">
                                            <span class="badge badge-soft-{{ $complianceRate >= 80 ? 'success' : ($complianceRate >= 60 ? 'warning' : 'danger') }}">
                                                {{ $complianceRate >= 80 ? 'Excellent' : ($complianceRate >= 60 ? 'Good' : 'Needs Improvement') }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Initialize counter animations
    $(document).ready(function() {
        $('[data-plugin="counterup"]').each(function() {
            var $this = $(this);
            var countTo = $this.text();
            
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'linear',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(this.countNum);
                }
            });
        });
    });
</script>
@endpush
