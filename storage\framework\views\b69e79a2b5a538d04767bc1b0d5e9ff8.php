<!DOCTYPE html>
<html class="no-js" lang="en">
<meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->

<head>
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <title>Login || Diagnostic Intelligence</title>
    <meta name="csrf-token" content="LjkuUqlaSDW4X740h7HW6tT5rFW58frkItXzEuOO" />
    <meta name="description" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <!-- Custom Meta -->
    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="uploads/website-images/favicon.png" />
    <!-- CSS here -->
    <link rel="stylesheet" href="frontend/css/bootstrap.min.css" />
    <link rel="stylesheet" href="frontend/css/animate.min.css" />
    <link rel="stylesheet" href="frontend/css/magnific-popup.css" />
    <link rel="stylesheet" href="frontend/css/fontawesome-all.min.css" />
    <link rel="stylesheet" href="frontend/css/flaticon-skillgro.css" />
    <link rel="stylesheet" href="frontend/css/swiper-bundle.min.css" />
    <link rel="stylesheet" href="frontend/css/default-icons.css" />
    <link rel="stylesheet" href="frontend/css/select2.min.css" />
    <link rel="stylesheet" href="frontend/css/odometer.css" />
    <link rel="stylesheet" href="frontend/css/aos.css" />
    <link rel="stylesheet" href="frontend/css/plyr.css" />
    <link rel="stylesheet" href="frontend/css/spacing.css" />
    <link rel="stylesheet" href="frontend/css/tg-cursor.css" />
    <link rel="stylesheet" href="frontend/css/bootstrap-datepicker.min.css" />
    <link rel="stylesheet" href="global/toastr/toastr.min.css" />
    <link rel="stylesheet" href="global/nice-select/nice-select.css" />
    <link rel="stylesheet" href="frontend/css/main.minc669.css?v=2.5.0" />
    <link rel="stylesheet" href="frontend/css/frontend.minc669.css?v=2.5.0" />

    <style>
        :root {
            --tg-theme-primary: #5751e1;
            --tg-theme-secondary: #ffc224;
            --tg-common-color-blue: #050071;
            --tg-common-color-blue-2: #282568;
            --tg-common-color-dark: #1c1a4a;
            --tg-common-color-black: #06042e;
            --tg-common-color-dark-2: #4a44d1;
        }
    </style>
    <!-- CustomCSS here -->
    <style>
        .tg-header__top {
            background: #fff;
        }
    </style>

    <script>
        "use strict";
      //write your javascript here without the script tag
    </script>
</head>

<body class="">
    <!-- Scroll-top -->
    <button class="scroll__top scroll-to-target" data-target="html" aria-label="Scroll Top">
        <i class="tg-flaticon-arrowhead-up"></i>
    </button>
    <!-- Scroll-top-end-->

    <!-- header-area -->
    <!-- header-area -->
    <?php echo $__env->make('includes.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- header-area-end -->
    <!-- header-area-end -->

    <!-- main-area -->
    <main class="main-area fix">
        <!-- breadcrumb-area -->
        <section class="breadcrumb__area breadcrumb__bg" data-background="uploads/website-images/breadcrumb-image.jpg">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="breadcrumb__content">
                            <h3 class="title">Login</h3>
                            <nav class="breadcrumb">
                                <span property="itemListElement" typeof="ListItem">
                                    <a href="index.html">Home</a>
                                </span>
                                <span class="breadcrumb-separator"><i class="fas fa-angle-right"></i></span>
                                <span property="itemListElement" typeof="ListItem">
                                    <a href="javascript:;">Login</a>
                                </span>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div class="breadcrumb__shape-wrap">
                <img src="frontend/img/others/breadcrumb_shape01.svg" alt="img" class="alltuchtopdown" />
                <img src="frontend/img/others/breadcrumb_shape02.svg" alt="img" data-aos="fade-right"
                    data-aos-delay="300" />
                <img src="frontend/img/others/breadcrumb_shape03.svg" alt="img" data-aos="fade-up"
                    data-aos-delay="400" />
                <img src="frontend/img/others/breadcrumb_shape04.svg" alt="img" data-aos="fade-down-left"
                    data-aos-delay="400" />
                <img src="frontend/img/others/breadcrumb_shape05.svg" alt="img" data-aos="fade-left"
                    data-aos-delay="400" />
            </div>
        </section>
        <!-- breadcrumb-area-end -->

        <!-- singUp-area -->
        <section class="singUp-area section-py-120">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-xl-6 col-lg-8">
                        <div class="singUp-wrap">
                            <h2 class="title">Welcome back!</h2>
                            <p>
                                Hey there! Ready to log in? Just enter your email and password
                                below and you will be back in action in no time. Lets go!
                            </p>
                            <div class="account__social">
                                <a href="https://accounts.google.com/o/oauth2/auth?client_id=************-isfa96f7lhqjpful1gmha1n4pl6e9dj8.apps.googleusercontent.com&amp;redirect_uri=https%3A%2F%2Fskillgro.websolutionus.com%2Fauth%2Fgoogle%2Fcallback&amp;scope=openid+profile+email&amp;response_type=code&amp;state=VtmfcCXuRyxYLCMCRuUxNbMdMYARUactEIfjk9MT"
                                    class="account__social-btn">
                                    <img src="frontend/img/icons/google.svg" alt="img" />
                                    Continue with google
                                </a>
                            </div>
                            <div class="account__divider">
                                <span>or</span>
                            </div>
                            <form method="POST" action="<?php echo e(route('login')); ?>"
                                class="account__form">
                                <?php echo csrf_field(); ?>

                                <div class="form-grp">
                                    <label for="email">Email <code>*</code></label>
                                    <input id="email" type="text" placeholder="email" value="" name="email" />
                                </div>
                                <div class="form-grp">
                                    <label for="password">Password <code>*</code></label>
                                    <input id="password" type="password" placeholder="password" name="password" />
                                </div>
                                <div class="account__check">
                                    <div class="account__check-remember">
                                        <input type="checkbox" class="form-check-input" name="remember" value=""
                                            id="terms-check" />
                                        <label for="terms-check" class="form-check-label">Remember me</label>
                                    </div>
                                    <div class="account__check-forgot">
                                        <a href="forgot-password.html">Forgot Password?</a>
                                    </div>
                                </div>
                                <!-- g-recaptcha -->
                                <button type="submit" class="btn btn-two arrow-btn">
                                    Sign In<img src="frontend/img/icons/right_arrow.svg" alt="img" class="injectable" />
                                </button>
                            </form>
                            <div class="account__switch">
                                <p>
                                    Dont have an account?<a href="<?php echo e(route('register')); ?>">Sign Up</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- singUp-area-end -->
    </main>
    <!-- main-area-end -->

    <!-- modal-area -->
    <!-- Modal -->
    <div class="modal fade dynamic-modal modal-lg" tabindex="-1" aria-labelledby="dynamic-modalLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="d-flex justify-content-center align-items:center p-3">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade bd-example-modal-lg" id="iframeModal" data-bs-backdrop="static" tabindex="-1"
        aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <h5>Your are using this website under an external iframe</h5>
                    <p>
                        For a better experience please browse directly instead of an
                        external iframe
                    </p>
                </div>
                <div class="modal-footer justify-content-center">
                    <a target="_blank" href="index.html" class="btn btn-sm btn-primary">Browse Directly</a>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">
                        Chapter Title
                    </h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="https://skillgro.websolutionus.com/instructor/course-chapter//store"
                        class="instructor__profile-form" method="post">
                        <input type="hidden" name="_token" value="LjkuUqlaSDW4X740h7HW6tT5rFW58frkItXzEuOO"
                            autocomplete="off" />
                        <div class="col-md-12">
                            <div class="form-grp">
                                <label for="title">Title <code>*</code></label>
                                <input id="title" name="title" type="text" value="" />
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Create</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- modal-area -->

  <?php echo $__env->make('includes.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- JS here -->
    <script src="global/js/jquery-3.7.1.min.js"></script>
    <script src="frontend/js/proper.min.js"></script>
    <script src="frontend/js/bootstrap.min.js"></script>
    <script src="frontend/js/imagesloaded.pkgd.min.js"></script>
    <script src="frontend/js/jquery.magnific-popup.min.js"></script>
    <script src="frontend/js/jquery.odometer.min.js"></script>
    <script src="frontend/js/jquery.appear.js"></script>
    <script src="frontend/js/tween-max.min.js"></script>
    <script src="frontend/js/select2.min.js"></script>
    <script src="frontend/js/swiper-bundle.min.js"></script>
    <script src="frontend/js/jquery.marquee.min.js"></script>
    <script src="frontend/js/tg-cursor.min.js"></script>
    <script src="frontend/js/svg-inject.min.js"></script>
    <script src="frontend/js/jquery.circleType.js"></script>
    <script src="frontend/js/jquery.lettering.min.js"></script>
    <script src="frontend/js/bootstrap-datepicker.min.js"></script>
    <script src="frontend/js/plyr.min.js"></script>
    <script src="frontend/js/wow.min.js"></script>
    <script src="frontend/js/aos.js"></script>
    <script src="frontend/js/vivus.min.js"></script>
    <script src="global/toastr/toastr.min.js"></script>
    <script src="frontend/js/sweetalert.js"></script>
    <script src="frontend/js/default/frontendc669.js?v=2.5.0"></script>
    <script src="frontend/js/default/cartc669.js?v=2.5.0"></script>
    <script src="global/nice-select/jquery.nice-select.min.js"></script>
    <!-- File Manager js-->
    <script src="vendor/laravel-filemanager/js/stand-alone-button.js"></script>

    <script src="frontend/js/mainc669.js?v=2.5.0"></script>

    <script>
        $(".file-manager").filemanager("file", {
        prefix: "https://skillgro.websolutionus.com/frontend-filemanager",
      });
      $(".file-manager-image").filemanager("image", {
        prefix: "https://skillgro.websolutionus.com/frontend-filemanager",
      });

      SVGInject(document.querySelectorAll("img.injectable"));
    </script>

    <!-- dynamic Toastr Notification -->
    <script>
        "use strict";
      toastr.options.closeButton = true;
      toastr.options.progressBar = true;
      toastr.options.positionClass = "toast-bottom-right";

      $(".datepicker").datepicker({
        format: "yyyy-mm-dd",
        orientation: "bottom auto",
      });
    </script>

    <!-- Toastr -->

    <!-- Google reCAPTCHA -->

    <!-- tawk -->

    <!-- Cookie Consent -->
    <script src="frontend/js/cookieconsent.min.js"></script>

    <script>
        "use strict";
      window.addEventListener("load", function () {
        window.wpcc.init({
          border: "normal",
          corners: "none",
          colors: {
            popup: {
              background: "#5751e1",
              text: "#fafafa !important",
              border: "#5751e1",
            },
            button: {
              background: "#fffceb",
              text: "#222758",
            },
          },
          content: {
            href: "https://skillgro.websolutionus.com/page/privacy-policy",
            message:
              "This website uses essential cookies to ensure its proper operation and tracking cookies to understand how you interact with it. The latter will be set only upon approval.",
            link: "More Info",
            button: "Yes",
          },
        });
      });
    </script>

    <script>
        if ($(".marquee_mode").length) {
        $(".marquee_mode").marquee({
          speed: 20,
          gap: 35,
          delayBeforeStart: 0,
          direction: "left",
          duplicated: true,
          pauseOnHover: true,
          startVisible: true,
        });
      }
    </script>

    <script>
        $(document).on("click", ".wpcc-btn", function () {
        $(".wpcc-container").fadeOut(1000);
      });
    </script>

    <!-- Language Translation Variables -->
    <script>
        var base_url = "index.html";
      var preloader_path =
        "uploads/custom-images/wsus-img-2024-06-06-05-37-49-1116.svg";

      var demo_mode_error = "This Is Demo Version. You Can Not Change Anything";
      var translation_success = "Translated Successfully!";
      var translation_processing = "Translation Processing, please wait...";
      var search_instructor_placeholder =
        "Search for an instructor with email or name";
      var Previous = "Previous";
      var Next = "Next";
      var basic_error_message = "Something went wrong";
      var discount = "Discount";
      var subscribe_now = "Subscribe Now";
      var submitting = "Submitting...";
      var submitting = "Submitting...";
      var login_first = "Login first";
    </script>
    <!-- Page specific js -->
    <script>
        "use strict";
      //write your javascript here without the script tag
    </script>
</body>


</html>
<?php /**PATH D:\Laravel-Apps\lernovate\resources\views/auth/login.blade.php ENDPATH**/ ?>