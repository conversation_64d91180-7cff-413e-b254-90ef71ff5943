<form id="send-verification" method="post" action="<?php echo e(route('verification.send')); ?>">
    <?php echo csrf_field(); ?>
</form>

<form method="post" action="<?php echo e(route('profile.update')); ?>" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>
    <?php echo method_field('patch'); ?>

    <div class="mb-3">
        <label class="form-label"><?php echo e(__('Profile Picture')); ?></label>
        <div class="d-flex align-items-center">
            <div class="me-3">
                <?php if($user->profile_image): ?>
                    <img src="<?php echo e(asset('storage/' . $user->profile_image)); ?>" alt="Profile" class="rounded-circle" width="60" height="60" style="object-fit: cover;">
                <?php else: ?>
                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 60px; height: 60px; font-size: 24px;">
                        <?php echo e(substr($user->name, 0, 1)); ?>

                    </div>
                <?php endif; ?>
            </div>
            <div>
                <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                <small class="text-muted">Upload JPG, PNG or GIF (max 2MB)</small>
            </div>
        </div>
        <?php $__errorArgs = ['profile_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-danger mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="mb-3">
        <label for="name" class="form-label"><?php echo e(__('Name')); ?></label>
        <input type="text" class="form-control" id="name" name="name" value="<?php echo e(old('name', $user->name)); ?>" required autofocus autocomplete="name">
        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-danger mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="mb-3">
        <label for="email" class="form-label"><?php echo e(__('Email')); ?></label>
        <input type="email" class="form-control" id="email" name="email" value="<?php echo e(old('email', $user->email)); ?>" required autocomplete="username">
        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-danger mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

        <?php if($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! $user->hasVerifiedEmail()): ?>
            <div class="mt-2">
                <p class="text-muted small">
                    <?php echo e(__('Your email address is unverified.')); ?>

                    <button form="send-verification" class="btn btn-link p-0 text-decoration-underline small">
                        <?php echo e(__('Click here to re-send the verification email.')); ?>

                    </button>
                </p>

                <?php if(session('status') === 'verification-link-sent'): ?>
                    <p class="text-success small mt-1">
                        <?php echo e(__('A new verification link has been sent to your email address.')); ?>

                    </p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <div class="d-flex align-items-center gap-2">
        <button type="submit" class="btn btn-primary"><?php echo e(__('Save')); ?></button>

        <?php if(session('status') === 'profile-updated'): ?>
            <span class="text-success small"><?php echo e(__('Saved.')); ?></span>
        <?php endif; ?>
    </div>
</form>
<?php /**PATH D:\Laravel-Apps\lernovate\resources\views/profile/partials/update-profile-information-form.blade.php ENDPATH**/ ?>