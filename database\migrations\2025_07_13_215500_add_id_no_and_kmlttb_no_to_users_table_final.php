<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'id_no')) {
                $table->string('id_no', 8)->nullable()->after('email');
            }
            if (!Schema::hasColumn('users', 'kmlttb_no')) {
                $table->string('kmlttb_no')->nullable()->after('id_no');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'id_no')) {
                $table->dropColumn('id_no');
            }
            if (Schema::hasColumn('users', 'kmlttb_no')) {
                $table->dropColumn('kmlttb_no');
            }
        });
    }
};
