<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Enrollment;
use App\Models\Certificate;
use App\Models\Assessment;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserDashboardController extends Controller
{
    public function dashboard()
    {
        $user = Auth::user();
        
        // Get user statistics
        $enrolledCourses = Enrollment::where('user_id', $user->id)->count();
        $completedCourses = Enrollment::where('user_id', $user->id)->completed()->count();
        $certificates = Certificate::where('user_id', $user->id)->count();
        $studyHours = Enrollment::where('user_id', $user->id)->sum('progress') * 0.5; // Estimate
        
        // Get current courses with progress
        $currentCourses = Enrollment::with(['course', 'course.zoomMeetings'])
            ->where('user_id', $user->id)
            ->latest()
            ->take(5)
            ->get();
        
        return view('user.dashboard', compact(
            'enrolledCourses', 
            'completedCourses', 
            'certificates', 
            'studyHours',
            'currentCourses'
        ));
    }

    public function enrolledCourses()
    {
        $enrollments = Enrollment::with(['course', 'course.zoomMeetings'])
            ->where('user_id', Auth::id())
            ->whereIn('status', ['enrolled', 'in_progress']) // Include both enrolled and in_progress
            ->latest('enrolled_at')
            ->paginate(10);

        return view('user.courses.enrolled', compact('enrollments'));
    }

    public function completedCourses()
    {
        $enrollments = Enrollment::with(['course', 'course.zoomMeetings'])
            ->where('user_id', Auth::id())
            ->completed()
            ->paginate(10);

        return view('user.courses.completed', compact('enrollments'));
    }

    public function courseProgress()
    {
        $enrollments = Enrollment::with('course')
            ->where('user_id', Auth::id())
            ->get();
        
        return view('user.courses.progress', compact('enrollments'));
    }

    public function certificates()
    {
        $certificates = Certificate::with('course')
            ->where('user_id', Auth::id())
            ->latest()
            ->paginate(10);
        
        return view('user.certificates.index', compact('certificates'));
    }

    public function assessments()
    {
        $assessments = Assessment::with('course')
            ->where('user_id', Auth::id())
            ->latest()
            ->paginate(10);
        
        return view('user.assessments.index', compact('assessments'));
    }

    public function diagnosticCourses($category = null)
    {
        $query = Course::published();
        
        if ($category) {
            $categoryMap = [
                'medical-imaging' => 'Medical Imaging',
                'laboratory-diagnostics' => 'Laboratory Diagnostics',
                'clinical-decision-making' => 'Clinical Decision Making',
                'pathology' => 'Pathology'
            ];
            
            if (isset($categoryMap[$category])) {
                $query->whereHas('category', function($q) use ($categoryMap, $category) {
                    $q->where('name', 'like', '%' . $categoryMap[$category] . '%');
                });
            }
        }
        
        $courses = $query->paginate(12);
        
        return view('user.courses.diagnostic', compact('courses', 'category'));
    }
}